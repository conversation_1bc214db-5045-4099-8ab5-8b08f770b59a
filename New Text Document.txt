local a=loadstring(game:HttpGet('https://sirius.menu/rayfield'))()local b=game:GetService("Players")local c=game:GetService("ReplicatedStorage")local d=game:GetService("RunService")local e=game:GetService("UserInputService")local f=b.LocalPlayer;local g=f.PlayerGui;local h=c.GameEvents;local workspace=workspace;local i=workspace.CurrentCamera;local j=Instance.new("VirtualInputManager")local k={}local function l(m)if type(m)~="string"then return false end;return string.split(m,"\0")[1]end;local function n(self,...)return self,{...}end;local function o(...)local p;p=function(...)local self,q=...local r=p(...)if type(q)=="string"and l(q)=="VirtualInputManager"then error(("'%s' is not a valid Service name"):format(l(q)))return end;return r end end;local OldFindService=hookfunction(game.FindService,function(...)local self,q=...local r=OldFindService(...)if type(q)=="string"and l(q)=="VirtualInputManager"then return end;return r end)o(game.GetService)o(game.getService)o(game.service)local s;s=hookmetamethod(game,"__namecall",function(...)local self,t=n(...)local u=getnamecallmethod()if typeof(self)=="Instance"and self==game and u:lower():match("service")and l(t[1])=="VirtualInputManager"then if u=="FindService"then return end;local v,w=pcall(function()setnamecallmethod(u)game[u](game,"VirtualFuckOff")end)if not w:match("is not a valid member")then error(w:replace("VirtualFuckOff","VirtualInputManager"))return end end;return s(...)end)local x;x=hookmetamethod(e.WindowFocused,"__index",function(...)local self,q=...local r=x(...)if type(r)~="function"and(tostring(self):find("WindowFocused")or tostring(self):find("WindowFocusReleased"))and not table.find(k,r)then table.insert(k,r)if q:lower()=="wait"then local y;y=hookfunction(r,function(...)local z=...if z==self then z=Instance.new("BindableEvent").Event end;return y(z)end)elseif q:lower()=="connect"then local y;y=hookfunction(r,function(...)local z,A=...if z==self then A=function()return end end;return y(z,A)end)end end;return r end)for B,C in next,getconnections(e.WindowFocusReleased)do C:Disable()end;for B,C in next,getconnections(e.WindowFocused)do C:Disable()end;if not iswindowactive()and not getgenv().WindowFocused then firesignal(e.WindowFocused)getgenv().WindowFocused=true end;local D=false;local E=nil;local function F()if E then return end;E=spawn(function()while D do j:SendKeyEvent(true,Enum.KeyCode.Unknown,false,game)task.wait(Random.new():NextNumber(15,120))end end)end;local function G()D=false;if E then E=nil end end;local H=nil;local function I()H=workspace:FindFirstChild("ScriptSettings")if not H then H=Instance.new("Folder")H.Name="ScriptSettings"H.Parent=workspace end;local J=H:FindFirstChild(f.Name)if not J then J=Instance.new("Folder")J.Name=f.Name;J.Parent=H end;return J end;local function K(L,M)local J=I()local N=J:FindFirstChild(L)if N then N:Destroy()end;local O=Instance.new("StringValue")O.Name=L;O.Value=game:GetService("HttpService"):JSONEncode(M)O.Parent=J end;local function P(L,Q)local J=I()local O=J:FindFirstChild(L)if O and O:IsA("StringValue")then local R,S=pcall(function()return game:GetService("HttpService"):JSONDecode(O.Value)end)if R then return S end end;return Q end;local T={}local U={Selected={}}local V={}local W=false;local X=nil;local Y={}local Z={Selected={}}local _={}local a0=false;local a1=nil;local a2={}local a3={Selected={}}local a4={}local a5=false;local a6=nil;local function a7(a8)local a9=g:FindFirstChild("Seed_Shop")if not a9 then return a8 and{}or T end;local aa=a9:FindFirstChild("Blueberry",true)if not aa then return a8 and{}or T end;aa=aa.Parent;local ab={}for ac,ad in pairs(aa:GetChildren())do local ae=ad:FindFirstChild("Main_Frame")if ae then local af=ae:FindFirstChild("Stock_Text")if af then local ag=tonumber(af.Text:match("%d+"))or 0;if a8 then if ag>0 then ab[ad.Name]=ag end else T[ad.Name]=ag end end end end;return a8 and ab or T end;local function ah(ai)local aj=h:FindFirstChild("BuySeedStock")if aj then aj:FireServer(ai)end end;local function ak(a8)local al=g:FindFirstChild("Gear_Shop")if not al then return a8 and{}or Y end;local aa=al:FindFirstChild("WateringCan")if not aa then for ac,am in pairs(al:GetDescendants())do if am:IsA("Frame")and am:FindFirstChild("Main_Frame")then aa=am.Parent;break end end end;if not aa then return a8 and{}or Y end;local ab={}for ac,ad in pairs(aa:GetChildren())do local ae=ad:FindFirstChild("Main_Frame")if ae then local af=ae:FindFirstChild("Stock_Text")if af then local ag=tonumber(af.Text:match("%d+"))or 0;if a8 then if ag>0 then ab[ad.Name]=ag end else Y[ad.Name]=ag end end end end;return a8 and ab or Y end;local function an(ao)local aj=h:FindFirstChild("BuyGearStock")if aj then aj:FireServer(ao)end end;local function ap(a8)local aq=g:FindFirstChild("PetShop_UI")or g:FindFirstChild("Pet_Egg_Shop")or g:FindFirstChild("Egg_Shop")or g:FindFirstChild("PetEggShop")or g:FindFirstChild("EggShop")if not aq then return a8 and{}or a2 end;local aa=nil;for ac,am in pairs(aq:GetDescendants())do if am:IsA("Frame")and am:FindFirstChild("Main_Frame")then aa=am.Parent;break end end;if not aa then for ac,am in pairs(aq:GetDescendants())do if am:IsA("ScrollingFrame")or am:IsA("Frame")then local ar=0;for ac,as in pairs(am:GetChildren())do if as:IsA("Frame")then ar=ar+1 end end;if ar>1 then aa=am;break end end end end;if not aa then return a8 and{}or a2 end;local ab={}for ac,ad in pairs(aa:GetChildren())do if ad:IsA("Frame")then local ae=ad:FindFirstChild("Main_Frame")if ae then local af=ae:FindFirstChild("Stock_Text")if af then local ag=tonumber(af.Text:match("%d+"))or 0;if a8 then if ag>0 then ab[ad.Name]=ag end else a2[ad.Name]=ag end end end end end;return a8 and ab or a2 end;local function at(au)local av={"BuyEggStock","BuyEgg","PurchaseEgg","BuyPetEgg","PetEggPurchase","EggPurchase","BuyPet","PurchasePet"}for ac,aw in pairs(av)do local ax=h:FindFirstChild(aw)if ax and ax:IsA("RemoteEvent")then ax:FireServer(au)break end end end;local function ay()local az=a3.Selected;if type(az)~="table"then return end;local aA={}for aB,C in pairs(az)do if C==true or type(C)=="string"and C~=""then table.insert(aA,type(aB)=="string"and aB or C)elseif type(aB)=="number"and type(C)=="string"and C~=""then table.insert(aA,C)end end;if#aA==0 then return end;ap()for ac,aC in pairs(aA)do local aD=a2[aC]if aD and aD>0 then for B=1,aD do at(aC)end end end end;local function aE()local aF=Z.Selected;if type(aF)~="table"then return end;local aG={}for aB,C in pairs(aF)do if C==true or type(C)=="string"and C~=""then table.insert(aG,type(aB)=="string"and aB or C)elseif type(aB)=="number"and type(C)=="string"and C~=""then table.insert(aG,C)end end;if#aG==0 then return end;ak()for ac,aH in pairs(aG)do local aD=Y[aH]if aD and aD>0 then for B=1,aD do an(aH)end end end end;local function aI()local aJ=U.Selected;if type(aJ)~="table"then return end;local aK={}for aB,C in pairs(aJ)do if C==true or type(C)=="string"and C~=""then table.insert(aK,type(aB)=="string"and aB or C)elseif type(aB)=="number"and type(C)=="string"and C~=""then table.insert(aK,C)end end;if#aK==0 then return end;a7()for ac,aL in pairs(aK)do local aD=T[aL]if aD and aD>0 then for B=1,aD do ah(aL)end end end end;local function aM()X=d.Heartbeat:Connect(function()if not W or not U.Selected then return end;local aJ=U.Selected;if type(aJ)~="table"then return end;local aN=false;for aB,C in pairs(aJ)do if C==true or type(C)=="string"and C~=""then aN=true;break end end;if not aN then return end;local aO=a7()local aP=false;local aQ={}for aB,C in pairs(aJ)do if C==true or type(C)=="string"and C~=""then local aL=type(aB)=="string"and aB or C;if type(aL)=="string"and aL~=""then table.insert(aQ,aL)end elseif type(aB)=="number"and type(C)=="string"and C~=""then table.insert(aQ,C)end end;for ac,aL in pairs(aQ)do local aR=V[aL]or-1;local aS=aO[aL]or 0;if aR~=aS then V[aL]=aS;if aS>0 then aP=true end end end;if aP then aI()end end)end;local function aT()if X then X:Disconnect()X=nil end end;local function aU()a1=d.Heartbeat:Connect(function()if not a0 or not Z.Selected then return end;local aF=Z.Selected;if type(aF)~="table"then return end;local aV=false;for aB,C in pairs(aF)do if C==true or type(C)=="string"and C~=""then aV=true;break end end;if not aV then return end;local aO=ak()local aP=false;local aW={}for aB,C in pairs(aF)do if C==true or type(C)=="string"and C~=""then local aH=type(aB)=="string"and aB or C;if type(aH)=="string"and aH~=""then table.insert(aW,aH)end elseif type(aB)=="number"and type(C)=="string"and C~=""then table.insert(aW,C)end end;for ac,aH in pairs(aW)do local aR=_[aH]or-1;local aS=aO[aH]or 0;if aR~=aS then _[aH]=aS;if aS>0 then aP=true end end end;if aP then aE()end end)end;local function aX()if a1 then a1:Disconnect()a1=nil end end;local function aY()a6=d.Heartbeat:Connect(function()if not a5 or not a3.Selected then return end;local az=a3.Selected;if type(az)~="table"then return end;local aZ=false;for aB,C in pairs(az)do if C==true or type(C)=="string"and C~=""then aZ=true;break end end;if not aZ then return end;local aO=ap()local aP=false;local a_={}for aB,C in pairs(az)do if C==true or type(C)=="string"and C~=""then local aC=type(aB)=="string"and aB or C;if type(aC)=="string"and aC~=""then table.insert(a_,aC)end elseif type(aB)=="number"and type(C)=="string"and C~=""then table.insert(a_,C)end end;for ac,aC in pairs(a_)do local aR=a4[aC]or-1;local aS=aO[aC]or 0;if aR~=aS then a4[aC]=aS;if aS>0 then aP=true end end end;if aP then ay()end end)end;local function b0()if a6 then a6:Disconnect()a6=nil end end;local b1=a:CreateWindow({Name="หมา "..1.0,LoadingTitle="by XZery",LoadingSubtitle="Loading...",ConfigurationSaving={Enabled=true,FolderName="RayfieldScriptHub",FileName="grow-a-garden"},Discord={Enabled=false,Invite="noinvitelink",RememberJoins=true},KeySystem=false,KeySettings={Title="Untitled",Subtitle="Key System",Note="No method of obtaining the key is provided",FileName="Key",SaveKey=true,GrabKeyFromSite=false,Key={"Hello"}}})local b2={Main=b1:CreateTab("Main",4483362458),Seed=b1:CreateTab("Seed",4483362458),Gear=b1:CreateTab("Gear",4483362458),Egg=b1:CreateTab("Egg",4483362458)}local b3=b2.Main:CreateSlider({Name="Speed",Range={16,200},Increment=1,Suffix="",CurrentValue=16,Flag="SpeedSlider",Callback=function(b4)local b5=game.Players.LocalPlayer;local b6=b5.Character;if b6 and b6:FindFirstChild("Humanoid")then b6.Humanoid.WalkSpeed=b4 end end})local b7=b2.Main:CreateSlider({Name="Jump Power",Range={50,200},Increment=1,Suffix="",CurrentValue=50,Flag="JumpSlider",Callback=function(b4)local b5=game.Players.LocalPlayer;local b6=b5.Character;if b6 and b6:FindFirstChild("Humanoid")then b6.Humanoid.JumpPower=b4 end end})local b8=false;local b9=nil;local ba=nil;local bb=nil;local bc=50;local bd=nil;local be=nil;local bf=nil;local bg=e.TouchEnabled;local bh=Color3.fromRGB(0,162,255)local bi=Color3.fromRGB(255,87,87)local bj=Color3.fromRGB(255,255,255)local function bk()if not bg then return end;local bl=Instance.new("ScreenGui")bl.Name="FlyControls"bl.Parent=f.PlayerGui;bl.ResetOnSpawn=false;bf=Instance.new("Frame")bf.Name="FlyControlsFrame"bf.Size=UDim2.new(0,120,0,200)bf.Position=UDim2.new(1,-130,0.5,-100)bf.BackgroundTransparency=1;bf.Parent=bl;bd=Instance.new("TextButton")bd.Name="FlyUpButton"bd.Size=UDim2.new(0,100,0,80)bd.Position=UDim2.new(0,10,0,10)bd.BackgroundColor3=bh;bd.BorderSizePixel=0;bd.Text="UP"bd.TextColor3=bj;bd.TextScaled=true;bd.Font=Enum.Font.GothamBold;bd.Parent=bf;local bm=Instance.new("UICorner")bm.CornerRadius=UDim.new(0,10)bm.Parent=bd;be=Instance.new("TextButton")be.Name="FlyDownButton"be.Size=UDim2.new(0,100,0,80)be.Position=UDim2.new(0,10,0,110)be.BackgroundColor3=bi;be.BorderSizePixel=0;be.Text="DOWN"be.TextColor3=bj;be.TextScaled=true;be.Font=Enum.Font.GothamBold;be.Parent=bf;local bn=Instance.new("UICorner")bn.CornerRadius=UDim.new(0,10)bn.Parent=be end;local function bo()if bf and bf.Parent then bf.Parent:Destroy()end;bd=nil;be=nil;bf=nil end;local function bp()local b5=game.Players.LocalPlayer;local b6=b5.Character;if not b6 or not b6:FindFirstChild("HumanoidRootPart")then return end;local bq=b6.HumanoidRootPart;b9=Instance.new("BodyVelocity")b9.MaxForce=Vector3.new(9e9,9e9,9e9)b9.Velocity=Vector3.new(0,0,0)b9.Parent=bq;ba=Instance.new("BodyAngularVelocity")ba.MaxTorque=Vector3.new(0,9e9,0)ba.AngularVelocity=Vector3.new(0,0,0)ba.Parent=bq;bk()bb=d.Heartbeat:Connect(function()local br=Vector3.new(0,0,0)local bs=i.CFrame.LookVector;local bt=i.CFrame.RightVector;local bu=Vector3.new(0,1,0)if not bg then if e:IsKeyDown(Enum.KeyCode.W)then br=br+bs*bc end;if e:IsKeyDown(Enum.KeyCode.S)then br=br-bs*bc end;if e:IsKeyDown(Enum.KeyCode.A)then br=br-bt*bc end;if e:IsKeyDown(Enum.KeyCode.D)then br=br+bt*bc end;if e:IsKeyDown(Enum.KeyCode.Space)then br=br+bu*bc end;if e:IsKeyDown(Enum.KeyCode.LeftShift)then br=br-bu*bc end else local bv=b6.Humanoid.MoveDirection;if bv.Magnitude>0 then local bw=i.CFrame;local bx=bw:VectorToWorldSpace(Vector3.new(bv.X,0,-bv.Z))br=br+bx*bc end;if bd and bd.Parent then if not bd:GetAttribute("Connected")then bd:SetAttribute("Connected",true)bd.MouseButton1Down:Connect(function()bd:SetAttribute("Pressed",true)end)bd.MouseButton1Up:Connect(function()bd:SetAttribute("Pressed",false)end)end;if be and not be:GetAttribute("Connected")then be:SetAttribute("Connected",true)be.MouseButton1Down:Connect(function()be:SetAttribute("Pressed",true)end)be.MouseButton1Up:Connect(function()be:SetAttribute("Pressed",false)end)end;if bd:GetAttribute("Pressed")then br=br+bu*bc end;if be and be:GetAttribute("Pressed")then br=br-bu*bc end end end;b9.Velocity=br end)end;local function by()if b9 then b9:Destroy()b9=nil end;if ba then ba:Destroy()ba=nil end;if bb then bb:Disconnect()bb=nil end;bo()end;local bz=b2.Main:CreateToggle({Name="Fly",CurrentValue=false,Flag="FlyToggle",Callback=function(b4)b8=b4;if b8 then bp()else by()end end})local bA=b2.Main:CreateToggle({Name="Anti-AFK",CurrentValue=false,Flag="AntiAfkToggle",Callback=function(b4)D=b4;K("AntiAFK",b4)if D then F()else G()end end})local bB=b2.Main:CreateButton({Name="Reset Character",Callback=function()local b5=game.Players.LocalPlayer;if b5.Character then b5.Character:BreakJoints()end end})local bC=b2.Seed:CreateDropdown({Name="Select Seeds",Options={},CurrentOption={},MultipleOptions=true,Flag="SeedDropdown",Callback=function(b4)U.Selected=b4;K("SelectedSeeds",b4)a7()end})local function bD()local bE=a7(false)local bF={}for aL,ac in pairs(bE)do table.insert(bF,aL)end;bC:Refresh(bF,true)end;local bG=b2.Seed:CreateToggle({Name="Auto-Buy Seeds",CurrentValue=false,Flag="AutoBuyToggle",Callback=function(b4)W=b4;K("AutoBuySeeds",b4)if W then aM()else aT()end end})bD()local bH=b2.Gear:CreateDropdown({Name="Select Gears",Options={},CurrentOption={},MultipleOptions=true,Flag="GearDropdown",Callback=function(b4)Z.Selected=b4;K("SelectedGears",b4)ak()end})local function bI()local bJ=ak(false)local bK={}for aH,ac in pairs(bJ)do table.insert(bK,aH)end;bH:Refresh(bK,true)end;local bL=b2.Gear:CreateToggle({Name="Auto-Buy Gears",CurrentValue=false,Flag="GearAutoBuyToggle",Callback=function(b4)a0=b4;K("AutoBuyGears",b4)if a0 then aU()else aX()end end})bI()local bM=b2.Egg:CreateDropdown({Name="Select Eggs",Options={},CurrentOption={},MultipleOptions=true,Flag="EggDropdown",Callback=function(b4)a3.Selected=b4;K("SelectedEggs",b4)ap()end})local function bN()local bO=ap(false)local bP={}for aC,ac in pairs(bO)do table.insert(bP,aC)end;bM:Refresh(bP,true)end;local bQ=b2.Egg:CreateToggle({Name="Auto-Buy Eggs",CurrentValue=false,Flag="EggAutoBuyToggle",Callback=function(b4)a5=b4;K("AutoBuyEggs",b4)if a5 then aY()else b0()end end})bN()local function bR()local bS=P("SelectedSeeds",{})if type(bS)=="table"and#bS>0 then U.Selected=bS;bC:Set(bS)end;local bT=P("SelectedGears",{})if type(bT)=="table"and#bT>0 then Z.Selected=bT;bH:Set(bT)end;local bU=P("SelectedEggs",{})if type(bU)=="table"and#bU>0 then a3.Selected=bU;bM:Set(bU)end;local bV=P("AutoBuySeeds",false)if bV then bG:Set(bV)end;local bW=P("AutoBuyGears",false)if bW then bL:Set(bW)end;local bX=P("AutoBuyEggs",false)if bX then bQ:Set(bX)end;local bY=P("AntiAFK",false)if bY then bA:Set(bY)end;if#bS>0 or#bT>0 or#bU>0 or bV or bW or bX or bY then a:Notify({Title="Loaded Settings!",Content="Settings loaded from workspace",Duration=2})end end;spawn(function()task.wait(2)bR()end)